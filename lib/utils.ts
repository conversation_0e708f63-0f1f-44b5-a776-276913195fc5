import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Generate canonical URL for SEO
 * @param path - The path to append to the base URL (should start with /)
 * @returns Complete canonical URL
 */
export function getCanonicalUrl(path: string = '/'): string {
  // Standardized base URL with www subdomain
  const baseUrl = 'https://www.theconsultnow.com'

  // Ensure path starts with /
  const normalizedPath = path.startsWith('/') ? path : `/${path}`

  // Remove trailing slash except for root
  const cleanPath = normalizedPath === '/' ? '/' : normalizedPath.replace(/\/$/, '')

  return `${baseUrl}${cleanPath}`
}


