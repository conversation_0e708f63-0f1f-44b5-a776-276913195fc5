import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'Profile Settings - The Consult Now | Manage Your Account',
  description: 'Manage your profile settings, subscription details, and account preferences for The Consult Now AI resume screening platform.',
  alternates: {
    canonical: getCanonicalUrl('/profile'),
  },
  openGraph: {
    title: 'Profile Settings - The Consult Now | Manage Your Account',
    description: 'Manage your profile settings, subscription details, and account preferences for The Consult Now AI resume screening platform.',
    url: getCanonicalUrl('/profile'),
    siteName: 'The Consult Now',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Profile Settings - The Consult Now | Manage Your Account',
    description: 'Manage your profile settings, subscription details, and account preferences for The Consult Now AI resume screening platform.',
  },
  robots: {
    index: false,
    follow: false,
  },
}

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
