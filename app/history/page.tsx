"use client"

import type { <PERSON><PERSON><PERSON> } from 'next'
import React, { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { auth, db } from "@/lib/firebase"
import { collection, query, where, orderBy, getDocs } from "firebase/firestore"
import { useToast } from "@/hooks/use-toast"
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable"
import {
  Search,
  Calendar,
  Users,
  FileText,
  Briefcase,
  MapPin,
  Clock,
  ChevronRight,
  Loader2
} from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"

// Types for job history data
type JobHistoryItem = {
  id: string
  title: string
  location: string
  description: string
  skills: string[]
  minExperience?: number
  maxExperience?: number
  createdAt: any
  userId: string
  resumeCount: number
  shortlistedCount: number
  rejectedCount: number
  lastActivity: string
}

type Resume = {
  id: string
  name: string
  matchScore: number
  matchQuality: string
  status: string
  uploadedAt: string
  basicInfo?: {
    fullName: string
    email: string
    phone: string
    location: string
  }
}

const HistoryPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [jobHistory, setJobHistory] = useState<JobHistoryItem[]>([])
  const [selectedJob, setSelectedJob] = useState<JobHistoryItem | null>(null)
  const [selectedJobResumes, setSelectedJobResumes] = useState<Resume[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isLoadingResumes, setIsLoadingResumes] = useState(false)
  const [activeFilter, setActiveFilter] = useState<'all' | 'shortlisted' | 'rejected'>('all')
  const router = useRouter()
  const { toast } = useToast()

  // Check authentication
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setIsLoading(false)
      if (user) {
        setIsAuthenticated(true)
        fetchJobHistory(user.uid)
      } else {
        router.push("/login")
        toast({
          title: "Authentication required",
          description: "Please log in to access this page",
          variant: "destructive",
        })
      }
    })

    return () => unsubscribe()
  }, [router, toast])

  // Fetch job history for the user
  const fetchJobHistory = async (userId: string) => {
    try {
      // Use a simpler query without orderBy to avoid index requirement
      const jobsQuery = query(
        collection(db, "jobs"),
        where("userId", "==", userId)
      )

      const querySnapshot = await getDocs(jobsQuery)
      const jobs: JobHistoryItem[] = []

      querySnapshot.forEach((doc) => {
        const data = doc.data()
        const resumes = data.resumes || []

        // Calculate statistics
        const resumeCount = resumes.length
        const shortlistedCount = resumes.filter((r: any) => r.status === 'shortlisted').length
        const rejectedCount = resumes.filter((r: any) => r.status === 'rejected').length

        // Get last activity date
        const lastActivity = resumes.length > 0
          ? Math.max(...resumes.map((r: any) => new Date(r.lastUpdated || r.uploadedAt || data.createdAt.toDate()).getTime()))
          : data.createdAt.toDate().getTime()

        jobs.push({
          id: doc.id,
          title: data.title || "Untitled Job",
          location: data.location || "Location not specified",
          description: data.description || "",
          skills: Array.isArray(data.skills) ? data.skills : (data.skills ? data.skills.split(',').map((s: string) => s.trim()) : []),
          minExperience: data.minExperience,
          maxExperience: data.maxExperience,
          createdAt: data.createdAt,
          userId: data.userId,
          resumeCount,
          shortlistedCount,
          rejectedCount,
          lastActivity: new Date(lastActivity).toISOString()
        })
      })

      // Sort jobs by creation date (newest first) on the client side
      jobs.sort((a, b) => {
        const dateA = a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt)
        const dateB = b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt)
        return dateB.getTime() - dateA.getTime()
      })

      setJobHistory(jobs)
    } catch (error) {
      console.error("Error fetching job history:", error)
      toast({
        title: "Error loading history",
        description: "Failed to load job history. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Handle filter selection
  const handleFilterSelect = (filter: 'all' | 'shortlisted' | 'rejected') => {
    setActiveFilter(filter)
  }

  // Handle job selection
  const handleJobSelect = async (job: JobHistoryItem) => {
    setSelectedJob(job)
    setIsLoadingResumes(true)
    setActiveFilter('all') // Reset filter when selecting a new job

    try {
      // Fetch the full job document to get resumes
      const jobsQuery = query(
        collection(db, "jobs"),
        where("userId", "==", auth.currentUser?.uid)
      )

      const querySnapshot = await getDocs(jobsQuery)
      let jobResumes: Resume[] = []

      querySnapshot.forEach((doc) => {
        if (doc.id === job.id) {
          const data = doc.data()
          const resumes = data.resumes || []

          jobResumes = resumes.map((resume: any) => ({
            id: resume.id || `resume_${Date.now()}`,
            name: resume.basicInfo?.fullName || resume.name || "Unknown Candidate",
            matchScore: resume.matchScore || 0,
            matchQuality: resume.matchQuality || "medium",
            status: resume.status || resume.analysisStatus || "completed",
            uploadedAt: resume.uploadedAt || resume.lastUpdated || "",
            basicInfo: resume.basicInfo
          }))
        }
      })

      setSelectedJobResumes(jobResumes)
    } catch (error) {
      console.error("Error fetching job resumes:", error)
      toast({
        title: "Error loading resumes",
        description: "Failed to load resume data for this job.",
        variant: "destructive",
      })
    } finally {
      setIsLoadingResumes(false)
    }
  }

  // Filter jobs based on search term
  const filteredJobs = jobHistory.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // Filter resumes based on active filter
  const filteredResumes = selectedJobResumes.filter((resume) => {
    if (activeFilter === 'all') return true
    if (activeFilter === 'shortlisted') return resume.status === 'shortlisted'
    if (activeFilter === 'rejected') return resume.status === 'rejected'
    return true
  })



  // Format date helper
  const formatDate = (date: any) => {
    if (!date) return "Unknown date"
    const d = date.toDate ? date.toDate() : new Date(date)
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Format relative time helper
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else if (diffInHours < 168) { // 7 days
      return `${Math.floor(diffInHours / 24)}d ago`
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="h-8 w-8 rounded-full border-2 border-t-transparent border-[#1aa8e0] animate-spin" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="ml-24 mr-8 pt-8 pb-8">
        {/* Header */}
        <div className="mb-6 px-4">
          <h1 className="text-2xl font-bold text-[#1aa8e0]">Job Analysis History</h1>
        </div>

        {/* Resizable Layout */}
        <div className="px-4">
          <ResizablePanelGroup direction="horizontal" className="h-[calc(100vh-12rem)] rounded-lg border border-zinc-800">
          {/* Left Panel - Job List */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <div className="h-full flex flex-col">
              {/* Search and Filter */}
              <div className="p-4 border-b border-zinc-800 flex-shrink-0">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
                  <Input
                    placeholder="Search jobs, locations, or skills..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-zinc-900 border-zinc-700 text-white placeholder-zinc-400"
                  />
                </div>
              </div>

              {/* Job List */}
              <div className="flex-1 overflow-hidden">
                <ScrollArea className="h-full bg-zinc-950/50 rounded-lg">
                <div className="p-4">
                  {filteredJobs.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-zinc-600 mx-auto mb-4" />
                      <p className="text-zinc-400">No job analyses found</p>
                      <p className="text-sm text-zinc-500 mt-1">
                        {searchTerm ? "Try adjusting your search terms" : "Start by creating your first job analysis"}
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {filteredJobs.map((job) => (
                        <Card
                          key={job.id}
                          className={`cursor-pointer transition-all duration-200 hover:bg-zinc-800/50 ${
                            selectedJob?.id === job.id ? 'bg-zinc-800 border-[#1aa8e0]' : 'bg-zinc-900 border-zinc-700'
                          }`}
                          onClick={() => handleJobSelect(job)}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-2">
                              <h3 className="font-medium text-white truncate flex-1 mr-2">
                                {job.title}
                              </h3>
                              <ChevronRight className={`h-4 w-4 text-zinc-400 transition-transform ${
                                selectedJob?.id === job.id ? 'rotate-90' : ''
                              }`} />
                            </div>

                            <div className="flex items-center text-sm text-zinc-400 mb-2">
                              <MapPin className="h-3 w-3 mr-1" />
                              <span className="truncate">{job.location}</span>
                            </div>

                            <div className="flex items-center justify-between text-xs text-zinc-500 mb-3">
                              <div className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                {formatDate(job.createdAt)}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {formatRelativeTime(job.lastActivity)}
                              </div>
                            </div>

                            <div className="flex items-center justify-between">
                              <div className="flex space-x-4 text-xs">
                                <div className="flex items-center text-zinc-400">
                                  <Users className="h-3 w-3 mr-1" />
                                  {job.resumeCount}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>

          {/* Draggable Handle */}
          <ResizableHandle withHandle className="bg-zinc-800 hover:bg-zinc-700 transition-colors" />

          {/* Right Panel - Job Details */}
          <ResizablePanel defaultSize={60} minSize={40}>
            <div className="h-full flex flex-col">
              {selectedJob ? (
                <>
                  {/* Job Details Header */}
                  <div className="p-6 border-b border-zinc-800">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h2 className="text-2xl font-bold text-white mb-2">{selectedJob.title}</h2>
                        <div className="flex items-center text-zinc-400 mb-2">
                          <MapPin className="h-4 w-4 mr-2" />
                          {selectedJob.location}
                        </div>
                      </div>
                      <Button
                        onClick={() => router.push(`/resume-dashboard?jobId=${selectedJob.id}`)}
                        className="bg-[#1aa8e0] hover:bg-[#1aa8e0]/90 text-white"
                      >
                        View Dashboard
                      </Button>
                    </div>

                    {/* Job Statistics */}
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div
                        className={`p-3 rounded-lg text-center cursor-pointer transition-all duration-200 hover:bg-zinc-700 bg-zinc-800 ${
                          activeFilter === 'all' ? 'border-2 border-[#1aa8e0]' : 'border-2 border-transparent'
                        }`}
                        onClick={() => handleFilterSelect('all')}
                      >
                        <div className="text-2xl font-bold text-white">{selectedJob.resumeCount}</div>
                        <div className="text-xs text-zinc-400">Total Resumes</div>
                      </div>
                      <div
                        className={`p-3 rounded-lg text-center cursor-pointer transition-all duration-200 hover:bg-zinc-700 bg-zinc-800 ${
                          activeFilter === 'shortlisted' ? 'border-2 border-green-400' : 'border-2 border-transparent'
                        }`}
                        onClick={() => handleFilterSelect('shortlisted')}
                      >
                        <div className="text-2xl font-bold text-green-400">{selectedJob.shortlistedCount}</div>
                        <div className="text-xs text-zinc-400">Shortlisted</div>
                      </div>
                      <div
                        className={`p-3 rounded-lg text-center cursor-pointer transition-all duration-200 hover:bg-zinc-700 bg-zinc-800 ${
                          activeFilter === 'rejected' ? 'border-2 border-red-400' : 'border-2 border-transparent'
                        }`}
                        onClick={() => handleFilterSelect('rejected')}
                      >
                        <div className="text-2xl font-bold text-red-400">{selectedJob.rejectedCount}</div>
                        <div className="text-xs text-zinc-400">Rejected</div>
                      </div>
                    </div>

                    {/* Skills */}
                    {selectedJob.skills.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-zinc-300 mb-2">Required Skills</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedJob.skills.slice(0, 8).map((skill, index) => (
                            <Badge key={index} variant="outline" className="border-zinc-600 text-zinc-300">
                              {skill}
                            </Badge>
                          ))}
                          {selectedJob.skills.length > 8 && (
                            <Badge variant="outline" className="border-zinc-600 text-zinc-400">
                              +{selectedJob.skills.length - 8} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Resume Results */}
                  <div className="flex-1 p-6 flex flex-col min-h-0">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-white">Candidate Results</h3>
                      {isLoadingResumes && (
                        <Loader2 className="h-4 w-4 animate-spin text-[#1aa8e0]" />
                      )}
                    </div>

                    <div className="flex-1 flex flex-col">
                      <div className="flex-1 overflow-hidden">
                        <ScrollArea className="h-full bg-zinc-950/50 rounded-lg">
                        <div className="p-4">
                          {isLoadingResumes ? (
                            <div className="flex items-center justify-center py-8">
                              <div className="h-6 w-6 rounded-full border-2 border-t-transparent border-[#1aa8e0] animate-spin" />
                            </div>
                          ) : filteredResumes.length === 0 ? (
                            <div className="text-center py-8">
                              <Users className="h-12 w-12 text-zinc-600 mx-auto mb-4" />
                              <p className="text-zinc-400">
                                {activeFilter === 'all'
                                  ? "No candidates found"
                                  : activeFilter === 'shortlisted'
                                    ? "No shortlisted candidates"
                                    : "No rejected candidates"
                                }
                              </p>
                              <p className="text-sm text-zinc-500 mt-1">
                                {activeFilter === 'all'
                                  ? "Upload resumes to start analyzing candidates"
                                  : `No candidates have been ${activeFilter} yet`
                                }
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              {filteredResumes
                                .sort((a, b) => b.matchScore - a.matchScore)
                                .map((resume) => (
                                  <Card key={resume.id} className="bg-zinc-800 border-zinc-700">
                                    <CardContent className="p-4">
                                      <div className="flex items-center justify-between mb-2">
                                        <h4 className="font-medium text-white">{resume.name}</h4>
                                        <div className="flex items-center space-x-2">
                                          <Badge
                                            variant={resume.matchScore >= 80 ? "default" : resume.matchScore >= 60 ? "secondary" : "outline"}
                                            className={
                                              resume.matchScore >= 80
                                                ? "bg-green-600 text-white"
                                                : resume.matchScore >= 60
                                                ? "bg-yellow-600 text-white"
                                                : "border-red-600 text-red-400"
                                            }
                                          >
                                            {resume.matchScore}%
                                          </Badge>
                                          {resume.status === 'shortlisted' && (
                                            <Badge className="bg-green-500 text-white text-xs">
                                              Shortlisted
                                            </Badge>
                                          )}
                                          {resume.status === 'rejected' && (
                                            <Badge className="bg-red-500 text-white text-xs">
                                              Rejected
                                            </Badge>
                                          )}
                                        </div>
                                      </div>

                                      {resume.basicInfo && (
                                        <div className="text-sm text-zinc-400 space-y-1">
                                          {resume.basicInfo.email && (
                                            <div>{resume.basicInfo.email}</div>
                                          )}
                                          {resume.basicInfo.location && (
                                            <div className="flex items-center">
                                              <MapPin className="h-3 w-3 mr-1" />
                                              {resume.basicInfo.location}
                                            </div>
                                          )}
                                        </div>
                                      )}

                                      <div className="flex items-center justify-between mt-3 text-xs text-zinc-500">
                                        <span>Uploaded {formatRelativeTime(resume.uploadedAt)}</span>
                                        <Badge variant="outline" className="border-zinc-600 text-zinc-400 text-xs">
                                          {resume.matchQuality}
                                        </Badge>
                                      </div>
                                    </CardContent>
                                  </Card>
                                ))}
                            </div>
                          )}
                        </div>
                        </ScrollArea>
                      </div>
                    </div>


                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Briefcase className="h-16 w-16 text-zinc-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">Select a Job</h3>
                    <p className="text-zinc-400">
                      Choose a job from the list to view detailed analysis results
                    </p>
                  </div>
                </div>
              )}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
        </div>
      </div>
    </div>
  )
}

export default HistoryPage
