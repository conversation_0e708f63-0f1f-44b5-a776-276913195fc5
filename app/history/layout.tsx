import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'Job Analysis History - The Consult Now | View Past Resume Screenings',
  description: 'Access your complete job analysis history and past resume screening results. Review candidate matches, shortlisted profiles, and hiring decisions.',
  alternates: {
    canonical: getCanonicalUrl('/history'),
  },
  openGraph: {
    title: 'Job Analysis History - The Consult Now | View Past Resume Screenings',
    description: 'Access your complete job analysis history and past resume screening results. Review candidate matches, shortlisted profiles, and hiring decisions.',
    url: getCanonicalUrl('/history'),
    siteName: 'The Consult Now',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Job Analysis History - The Consult Now | View Past Resume Screenings',
    description: 'Access your complete job analysis history and past resume screening results. Review candidate matches, shortlisted profiles, and hiring decisions.',
  },
  robots: {
    index: false,
    follow: false,
  },
}

export default function HistoryLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
