/* Blog Content Spacing Styles */

.blog-content h1 {
  margin-top: 0 !important;
  margin-bottom: 3rem !important;
  line-height: 1.2 !important;
}

.blog-content h2 {
  margin-top: 4rem !important;
  margin-bottom: 2rem !important;
  line-height: 1.3 !important;
}

.blog-content h3 {
  margin-top: 3rem !important;
  margin-bottom: 1.5rem !important;
  line-height: 1.4 !important;
}

.blog-content p {
  margin-bottom: 2rem !important;
  line-height: 1.7 !important;
}

.blog-content ul,
.blog-content ol {
  margin-top: 1rem !important;
  margin-bottom: 2rem !important;
}

.blog-content li {
  margin-bottom: 0.75rem !important;
  line-height: 1.6 !important;
}

.blog-content table {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
  width: 100% !important;
  border-collapse: collapse !important;
}

.blog-content table th,
.blog-content table td {
  padding: 1rem !important;
  border: 1px solid #374151 !important;
  text-align: left !important;
}

.blog-content table th {
  background-color: #1f2937 !important;
  font-weight: 600 !important;
  color: white !important;
}

.blog-content table td {
  color: #d1d5db !important;
}

.blog-content table tr:nth-child(even) {
  background-color: #111827 !important;
}

.blog-content blockquote {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
  padding-left: 1.5rem !important;
  border-left: 4px solid #1aa8e0 !important;
  font-style: italic !important;
  color: #9ca3af !important;
}

.blog-content code {
  background-color: #1f2937 !important;
  color: #1aa8e0 !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.9em !important;
}

.blog-content pre {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
  padding: 1.5rem !important;
  background-color: #111827 !important;
  border-radius: 0.5rem !important;
  overflow-x: auto !important;
}

.blog-content pre code {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Ensure first and last elements have proper spacing */
.blog-content > *:first-child {
  margin-top: 0 !important;
}

.blog-content > *:last-child {
  margin-bottom: 0 !important;
}

/* Special spacing for FAQ sections */
.blog-content h3 + p {
  margin-top: 0.5rem !important;
}

/* Table responsive wrapper */
.blog-content table {
  display: block !important;
  overflow-x: auto !important;
  white-space: nowrap !important;
}

@media (min-width: 768px) {
  .blog-content table {
    display: table !important;
    white-space: normal !important;
  }
}
