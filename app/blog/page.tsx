import type { Metadata } from 'next'
import BlogPage from "@/components/blog-page"
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'AI Resume Screening Blog - Free Tools, Tips & Best Practices',
  description: 'Expert insights on free AI resume screening software, hiring automation, and recruitment best practices. Learn from industry professionals.',
  keywords: 'AI resume screening blog, recruitment tips, hiring best practices, free AI tools, resume screening software, HR technology insights',
  alternates: {
    canonical: getCanonicalUrl('/blog'),
  },
  openGraph: {
    title: 'AI Resume Screening Blog - Free Tools, Tips & Best Practices',
    description: 'Expert insights on free AI resume screening software, hiring automation, and recruitment best practices. Learn from industry professionals.',
    url: getCanonicalUrl('/blog'),
    siteName: 'The Consult Now',
    type: 'website',
  },
}

export default function Blog() {
  return <BlogPage />
}
