import type { Metadata } from 'next'
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, User } from "lucide-react"
import { notFound } from "next/navigation"
import { getBlogPostBySlug } from "@/lib/blog-data"
import { getCanonicalUrl } from '@/lib/utils'
import { BlogBackButton, BlogCTAButton } from "@/components/blog-navigation"
import { Breadcrumb, BreadcrumbSchema } from "@/components/breadcrumb"
import Script from 'next/script'

// Generate metadata for each blog post
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const post = getBlogPostBySlug(params.slug)

  if (!post) {
    return {
      title: 'Post Not Found - The Consult Now',
      description: 'The requested blog post could not be found.',
    }
  }

  return {
    title: `${post.title} - The Consult Now`,
    description: post.excerpt.length > 160 ? post.excerpt.substring(0, 157) + '...' : post.excerpt,
    keywords: `AI resume screening, ${post.category.toLowerCase()}, recruitment software, hiring tools, resume analysis, candidate matching`,
    alternates: {
      canonical: getCanonicalUrl(`/blog/${post.slug}`),
    },
    openGraph: {
      title: `${post.title} - The Consult Now`,
      description: post.excerpt,
      url: getCanonicalUrl(`/blog/${post.slug}`),
      siteName: 'The Consult Now',
      type: 'article',
      publishedTime: post.date,
      authors: [post.author],
      images: post.image ? [post.image] : undefined,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${post.title} - The Consult Now`,
      description: post.excerpt,
      images: post.image ? [post.image] : undefined,
    },
  }
}

export default function BlogPost({ params }: { params: { slug: string } }) {
  const slug = params.slug

  // Find the blog post by slug
  const post = getBlogPostBySlug(slug)

  if (!post) {
    notFound()
  }

  // Generate structured data for the blog post
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": post.image ? `https://www.theconsultnow.com${post.image}` : "https://www.theconsultnow.com/placeholder.svg",
    "author": {
      "@type": "Person",
      "name": post.author,
      "url": "https://www.theconsultnow.com"
    },
    "publisher": {
      "@type": "Organization",
      "name": "The Consult Now",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.theconsultnow.com/placeholder-logo.svg"
      }
    },
    "datePublished": new Date(post.date).toISOString(),
    "dateModified": new Date(post.date).toISOString(),
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": getCanonicalUrl(`/blog/${post.slug}`)
    },
    "articleSection": post.category,
    "keywords": ["AI resume screening", "recruitment technology", "hiring software", "resume analysis", "AI recruitment"],
    "wordCount": post.content ? post.content.replace(/<[^>]*>/g, '').split(' ').length : 1000
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Structured Data */}
      <Script
        id="blog-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <BreadcrumbSchema />

      {/* Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-black via-gray-900 to-black">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,_rgba(133,41,219,0.15)_0%,_transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,_rgba(26,168,224,0.15)_0%,_transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_40%_40%,_rgba(133,41,219,0.1)_0%,_transparent_50%)]" />
      </div>



      {/* Back to Blog */}
      <section className="relative z-10 max-w-4xl mx-auto px-8 py-12">
        <Breadcrumb />
        <BlogBackButton />
      </section>

      {/* Blog Post Content */}
      <article className="relative z-10 max-w-4xl mx-auto px-8 pb-24">
        {/* Hero Image */}
        <div className="relative h-64 md:h-96 mb-12 rounded-2xl overflow-hidden">
          <img
            src={post.image || "/placeholder.svg"}
            alt={`${post.title} - AI resume screening software dashboard showing candidate analysis and matching scores`}
            className="w-full h-full object-cover"
            loading="eager"
            width={800}
            height={400}
          />
          <div className="absolute top-6 left-6">
            <Badge className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] text-white border-0 px-4 py-2">
              {post.category}
            </Badge>
          </div>
        </div>

        {/* Post Header */}
        <header className="mb-12">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-8 leading-tight">
            {post.title}
          </h1>

          <div className="flex flex-wrap items-center gap-8 mb-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-r from-[#8529db] to-[#1aa8e0] rounded-full flex items-center justify-center">
                <User className="w-6 h-6 text-white" />
              </div>
              <span className="text-gray-300 text-lg">{post.author}</span>
            </div>
            <div className="flex items-center gap-3 text-gray-400">
              <Calendar className="w-5 h-5" />
              <span className="text-base">{post.date}</span>
            </div>
            <div className="flex items-center gap-3 text-gray-400">
              <Clock className="w-5 h-5" />
              <span className="text-base">{post.readTime}</span>
            </div>
          </div>
        </header>

        {/* Post Content */}
        <div
          className="prose prose-xl prose-invert max-w-none prose-headings:text-white prose-h2:text-4xl prose-h2:font-bold prose-h2:mb-8 prose-h2:mt-12 prose-h3:text-2xl prose-h3:font-semibold prose-h3:mb-6 prose-h3:mt-10 prose-p:text-gray-300 prose-p:leading-relaxed prose-p:mb-6 prose-p:text-lg prose-strong:text-white prose-strong:font-semibold"
          dangerouslySetInnerHTML={{ __html: post.content || '' }}
        />

        {/* Related Articles Section */}
        <div className="mt-16 p-8 bg-zinc-900/50 rounded-2xl border border-zinc-700">
          <h3 className="text-2xl font-bold text-white mb-6">Related Articles</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="/blog/best-free-ai-resume-screening-software-2025" className="block p-4 bg-zinc-800/50 rounded-lg hover:bg-zinc-700/50 transition-colors">
              <h4 className="text-white font-medium mb-2">Best Free AI Resume Screening Software in 2025</h4>
              <p className="text-gray-400 text-sm">Discover the top free AI tools for resume screening and candidate matching.</p>
            </a>
            <a href="/blog/free-ai-resume-parser" className="block p-4 bg-zinc-800/50 rounded-lg hover:bg-zinc-700/50 transition-colors">
              <h4 className="text-white font-medium mb-2">Free AI Resume Parser</h4>
              <p className="text-gray-400 text-sm">Learn how to extract structured data from resumes using AI technology.</p>
            </a>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 p-10 bg-gradient-to-r from-[#8529db]/20 to-[#1aa8e0]/20 rounded-2xl border border-[#8529db]/30">
          <h3 className="text-3xl font-bold text-white mb-6">Ready to transform your hiring process?</h3>
          <p className="text-gray-300 mb-8 text-lg leading-relaxed">
            Join thousands of HR professionals who have already revolutionized their recruitment with AI-powered screening.
          </p>
          <BlogCTAButton />
        </div>
      </article>
    </div>
  )
}
