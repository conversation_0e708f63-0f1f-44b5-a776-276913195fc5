import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'Start AI Resume Analysis - The Consult Now | Begin Screening Process',
  description: 'Start your AI-powered resume screening process. Create job descriptions and upload resumes to find the perfect candidates with intelligent matching.',
  alternates: {
    canonical: getCanonicalUrl('/start-analysing'),
  },
  openGraph: {
    title: 'Start AI Resume Analysis - The Consult Now | Begin Screening Process',
    description: 'Start your AI-powered resume screening process. Create job descriptions and upload resumes to find the perfect candidates with intelligent matching.',
    url: getCanonicalUrl('/start-analysing'),
    siteName: 'The Consult Now',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Start AI Resume Analysis - The Consult Now | Begin Screening Process',
    description: 'Start your AI-powered resume screening process. Create job descriptions and upload resumes to find the perfect candidates with intelligent matching.',
  },
  robots: {
    index: false,
    follow: false,
  },
}

export default function StartAnalysingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
