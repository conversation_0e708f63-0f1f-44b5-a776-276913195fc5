import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'Create Job Description - The Consult Now | AI Resume Screening Setup',
  description: 'Create detailed job descriptions for AI-powered resume screening. Define requirements, skills, and criteria to find the perfect candidates.',
  alternates: {
    canonical: getCanonicalUrl('/job-description-form'),
  },
  openGraph: {
    title: 'Create Job Description - The Consult Now | AI Resume Screening Setup',
    description: 'Create detailed job descriptions for AI-powered resume screening. Define requirements, skills, and criteria to find the perfect candidates.',
    url: getCanonicalUrl('/job-description-form'),
    siteName: 'The Consult Now',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Create Job Description - The Consult Now | AI Resume Screening Setup',
    description: 'Create detailed job descriptions for AI-powered resume screening. Define requirements, skills, and criteria to find the perfect candidates.',
  },
  robots: {
    index: false,
    follow: false,
  },
}

export default function JobDescriptionFormLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
