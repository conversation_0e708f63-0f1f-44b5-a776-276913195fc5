import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'Resume Dashboard - The Consult Now | View Candidate Analysis Results',
  description: 'View detailed resume analysis results, candidate match scores, and screening insights. Make informed hiring decisions with AI-powered candidate evaluation.',
  alternates: {
    canonical: getCanonicalUrl('/resume-dashboard'),
  },
  openGraph: {
    title: 'Resume Dashboard - The Consult Now | View Candidate Analysis Results',
    description: 'View detailed resume analysis results, candidate match scores, and screening insights. Make informed hiring decisions with AI-powered candidate evaluation.',
    url: getCanonicalUrl('/resume-dashboard'),
    siteName: 'The Consult Now',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Resume Dashboard - The Consult Now | View Candidate Analysis Results',
    description: 'View detailed resume analysis results, candidate match scores, and screening insights. Make informed hiring decisions with AI-powered candidate evaluation.',
  },
  robots: {
    index: false,
    follow: false,
  },
}

export default function ResumeDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
