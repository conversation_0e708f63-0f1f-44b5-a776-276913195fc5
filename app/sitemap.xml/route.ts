import { NextRequest, NextResponse } from 'next/server';
import zlib from 'zlib';

// Force dynamic rendering for this route since we need to check request headers
export const dynamic = 'force-dynamic';

/**
 * Generate comprehensive sitemap XML content with all important pages
 */
const generateSitemapXml = async (): Promise<string> => {
  const baseUrl = 'https://www.theconsultnow.com';
  const currentDate = new Date().toISOString();

  // Static pages with their priorities and change frequencies
  const staticPages = [
    { url: '/', priority: '1.0', changefreq: 'weekly', lastmod: currentDate },
    { url: '/pricing', priority: '0.9', changefreq: 'monthly', lastmod: currentDate },
    { url: '/blog', priority: '0.8', changefreq: 'weekly', lastmod: currentDate },
    { url: '/support', priority: '0.7', changefreq: 'monthly', lastmod: currentDate },
    { url: '/login', priority: '0.5', changefreq: 'yearly', lastmod: currentDate },
    { url: '/signup', priority: '0.6', changefreq: 'yearly', lastmod: currentDate },
    { url: '/forgot-password', priority: '0.3', changefreq: 'yearly', lastmod: currentDate },
  ];

  // Blog posts from blog data
  const blogPosts = [
    { slug: 'ai-resume-screening-software', date: '2025-01-16' },
    { slug: 'best-free-ai-resume-screening-software-2025', date: '2025-01-16' },
    { slug: 'free-ai-resume-parser', date: '2025-01-16' },
    { slug: 'ai-resume-screening-free-trial', date: '2025-01-16' },
    { slug: 'how-to-choose-best-free-ai-resume-screening-software-small-businesses', date: '2025-01-16' },
    { slug: 'top-10-features-best-free-ai-resume-screening-software', date: '2025-01-16' },
    { slug: '5-signs-you-need-best-free-ai-resume-screening-software-right-now', date: '2025-01-16' },
    { slug: 'why-small-hr-teams-love-best-free-ai-resume-screening-software', date: '2025-01-16' },
    { slug: 'free-ai-resume-screening-vs-manual-screening-time-cost-savings', date: '2025-01-16' },
  ];

  let sitemapXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  // Add static pages
  staticPages.forEach(page => {
    sitemapXml += `
  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
  });

  // Add blog posts
  blogPosts.forEach(post => {
    sitemapXml += `
  <url>
    <loc>${baseUrl}/blog/${post.slug}</loc>
    <lastmod>${post.date}T10:00:00+00:00</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`;
  });

  sitemapXml += `
</urlset>`;

  return sitemapXml;
};

/**
 * Function to get the latest sitemap XML content
 */
const getLatestSitemapXml = async (): Promise<string> => {
  return await generateSitemapXml();
};

/**
 * Compress string content using gzip
 */
const compressContent = (content: string): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    zlib.gzip(content, (err, compressed) => {
      if (err) {
        reject(err);
      } else {
        resolve(compressed);
      }
    });
  });
};

/**
 * GET /sitemap.xml
 * Serves the sitemap XML with proper headers and optional gzip compression
 */
export async function GET(request: NextRequest) {
  try {
    // Get the sitemap XML content
    const sitemapXml = await getLatestSitemapXml();

    // Check if client accepts gzip encoding
    const acceptEncoding = request.headers.get('accept-encoding') || '';
    const supportsGzip = acceptEncoding.includes('gzip');

    // Prepare response headers
    const headers = new Headers({
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=300', // 5 minutes cache
    });

    if (supportsGzip) {
      try {
        // Compress the content
        const compressedContent = await compressContent(sitemapXml);

        // Add gzip headers
        headers.set('Content-Encoding', 'gzip');
        headers.set('Content-Length', compressedContent.length.toString());

        // Return compressed response
        return new NextResponse(compressedContent, {
          status: 200,
          headers,
        });
      } catch (compressionError) {
        console.error('Failed to compress sitemap:', compressionError);
        // Fall back to uncompressed response
      }
    }

    // Return uncompressed response
    headers.set('Content-Length', Buffer.byteLength(sitemapXml, 'utf8').toString());

    return new NextResponse(sitemapXml, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error('Error serving sitemap:', error);

    return new NextResponse('Internal Server Error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
      },
    });
  }
}
