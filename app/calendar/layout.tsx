import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/lib/utils'

export const metadata: Metadata = {
  title: 'Calendar & Scheduling - The Consult Now | Manage Interview Schedule',
  description: 'Manage your interview calendar and scheduling for candidates. Organize meetings, track appointments, and streamline your hiring timeline.',
  alternates: {
    canonical: getCanonicalUrl('/calendar'),
  },
  openGraph: {
    title: 'Calendar & Scheduling - The Consult Now | Manage Interview Schedule',
    description: 'Manage your interview calendar and scheduling for candidates. Organize meetings, track appointments, and streamline your hiring timeline.',
    url: getCanonicalUrl('/calendar'),
    siteName: 'The Consult Now',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Calendar & Scheduling - The Consult Now | Manage Interview Schedule',
    description: 'Manage your interview calendar and scheduling for candidates. Organize meetings, track appointments, and streamline your hiring timeline.',
  },
  robots: {
    index: false,
    follow: false,
  },
}

export default function CalendarLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
